#!/bin/bash

# Script para instalar todas as aplicações Flatpak restantes
echo "🚀 Instalando aplicações Flatpak..."

# Lista das aplicações restantes
apps=(
    "com.google.AndroidStudio.flatpakref"
    "com.helix_editor.Helix.flatpakref"
    "com.protonvpn.www.flatpakref"
    "com.rustdesk.RustDesk.flatpakref"
    "com.usebottles.bottles.flatpakref"
    "in.srev.guiscrcpy.flatpakref"
    "md.obsidian.Obsidian.flatpakref"
    "org.flameshot.Flameshot.flatpakref"
    "org.gabmus.hydrapaper.flatpakref"
    "org.telegram.desktop.flatpakref"
    "ru.linux_gaming.PortProton.flatpakref"
)

# Contador
total=${#apps[@]}
current=0

# Instalar cada aplicação
for app in "${apps[@]}"; do
    current=$((current + 1))
    echo ""
    echo "📦 [$current/$total] Instalando $app..."
    
    if flatpak install --assumeyes "$app"; then
        echo "✅ $app instalado com sucesso!"
    else
        echo "❌ Erro ao instalar $app"
    fi
done

echo ""
echo "🎉 Instalação concluída!"
echo "💡 Reinicie a sessão para que as aplicações apareçam no menu."
