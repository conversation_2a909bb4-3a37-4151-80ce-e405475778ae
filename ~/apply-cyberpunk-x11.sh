#!/bin/bash

echo "🌆 APLICANDO TEMA CYBERPUNK X11 + NVIDIA 🌆"

# Aplicar configurações GTK
echo "📱 Configurando GTK..."
gsettings set org.gnome.desktop.interface gtk-theme 'Breeze-Dark'
gsettings set org.gnome.desktop.interface icon-theme 'breeze-dark'
gsettings set org.gnome.desktop.interface cursor-theme 'Layan-cursors'
gsettings set org.gnome.desktop.interface font-name 'JetBrains Mono Nerd Font 11'
gsettings set org.gnome.desktop.interface monospace-font-name 'JetBrains Mono Nerd Font 13'
gsettings set org.gnome.desktop.interface color-scheme 'prefer-dark'

# Configurar wallpaper
echo "🖼️ Configurando wallpaper cyberpunk..."
gsettings set org.gnome.desktop.background picture-uri ''
gsettings set org.gnome.desktop.background picture-uri-dark ''
gsettings set org.gnome.desktop.background primary-color '#0a0a0f'
gsettings set org.gnome.desktop.background color-shading-type 'solid'

# Configurar terminal padrão
echo "💻 Configurando terminal padrão..."
gsettings set org.gnome.desktop.default-applications.terminal exec 'kitty'

# Criar wallpaper cyberpunk se não existir
if [ ! -f ~/.config/wallpapers/cyberpunk.jpg ]; then
    echo "🎨 Criando wallpaper cyberpunk..."
    mkdir -p ~/.config/wallpapers
    convert -size 1920x1080 gradient:#0a0a0f-#1a0033 ~/.config/wallpapers/cyberpunk.jpg 2>/dev/null || {
        # Fallback: criar cor sólida
        convert -size 1920x1080 xc:#0a0a0f ~/.config/wallpapers/cyberpunk.jpg 2>/dev/null || {
            echo "⚠️ ImageMagick não disponível, usando cor sólida"
        }
    }
fi

# Recarregar cache de fontes
echo "🔤 Recarregando cache de fontes..."
fc-cache -fv > /dev/null 2>&1

# Criar arquivo .xsession para i3
echo "🔧 Configurando sessão X11..."
cat > ~/.xsession << 'EOF'
#!/bin/bash
# 🌆 CYBERPUNK X11 SESSION 🌆

# Start compositor
picom --config ~/.config/picom/picom.conf &

# Start i3
exec i3
EOF

chmod +x ~/.xsession

# Criar entrada de desktop para i3
echo "🖥️ Criando entrada de desktop..."
sudo mkdir -p /usr/share/xsessions
sudo tee /usr/share/xsessions/i3-cyberpunk.desktop > /dev/null << 'EOF'
[Desktop Entry]
Name=i3 Cyberpunk
Comment=Cyberpunk Neon Hacker Theme
Exec=i3
TryExec=i3
Type=Application
X-LightDM-DesktopName=i3-cyberpunk
DesktopNames=i3-cyberpunk
Keywords=tiling;wm;windowmanager;window;manager;cyberpunk;
EOF

echo ""
echo "✅ TEMA CYBERPUNK X11 APLICADO COM SUCESSO!"
echo ""
echo "🌆 CYBERPUNK NEON HACKER THEME ATIVADO!"
echo ""
echo "📋 Para usar o i3 com tema Cyberpunk:"
echo "1. Faça logout da sessão atual"
echo "2. Na tela de login, escolha 'i3 Cyberpunk' como sessão"
echo "3. Faça login normalmente"
echo ""
echo "🎯 Atalhos principais do i3:"
echo "• Super + Enter: Terminal Kitty (transparente + neon)"
echo "• Super + D: Rofi launcher (cyberpunk)"
echo "• Super + Shift + Q: Fechar janela"
echo "• Super + 1-9: Trocar workspace"
echo "• Super + Shift + C: Recarregar configuração"
echo ""
echo "🔥 Comandos cyberpunk disponíveis:"
echo "• matrix  - Efeito Matrix"
echo "• hack    - Simulação de hack"
echo "• neon    - Info do sistema neon"
echo "• cyber   - Menu de ferramentas"
echo "• btop    - Monitor do sistema"
echo ""
echo "🎨 Características do tema:"
echo "• Terminal Kitty transparente com cores neon"
echo "• Polybar com informações do sistema"
echo "• Bordas com glow effect neon"
echo "• Cores: Rosa neon, Ciano, Verde Matrix"
echo "• Efeitos de transparência e blur"
echo "• Workspaces com ícones cyberpunk"
echo "• Compatível com NVIDIA X11"
echo ""
echo "🚀 Reinicie o terminal para ver o banner cyberpunk!"
echo "💡 Use 'cyberpunk-banner' para ver o banner a qualquer momento"
