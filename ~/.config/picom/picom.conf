# Cyberpunk Picom Configuration
# Transparency and Effects

# Backend
backend = "glx";
glx-no-stencil = true;
glx-copy-from-front = false;

# Shadows
shadow = true;
shadow-radius = 15;
shadow-offset-x = -15;
shadow-offset-y = -15;
shadow-opacity = 0.8;
shadow-red = 0.5;
shadow-green = 0.0;
shadow-blue = 1.0;

shadow-exclude = [
    "name = 'Notification'",
    "class_g = 'Conky'",
    "class_g ?= 'Notify-osd'",
    "class_g = 'Cairo-clock'",
    "_GTK_FRAME_EXTENTS@:c"
];

# Opacity
inactive-opacity = 0.85;
active-opacity = 0.95;
frame-opacity = 0.9;
inactive-opacity-override = false;

opacity-rule = [
    "95:class_g = 'kitty' && focused",
    "85:class_g = 'kitty' && !focused",
    "90:class_g = 'Rofi'",
    "85:class_g = 'Code'",
    "90:class_g = 'firefox'",
    "95:class_g = 'discord'",
    "80:class_g = 'Polybar'",
    "85:class_g = 'Dunst'"
];

# Fading
fading = true;
fade-delta = 8;
fade-in-step = 0.03;
fade-out-step = 0.03;
fade-exclude = [ ];

# Blur
blur-background = true;
blur-background-frame = true;
blur-background-fixed = false;
blur-method = "dual_kawase";
blur-strength = 8;

blur-background-exclude = [
    "window_type = 'dock'",
    "window_type = 'desktop'",
    "_GTK_FRAME_EXTENTS@:c"
];

# Window type settings
wintypes: {
    tooltip = { fade = true; shadow = true; opacity = 0.9; focus = true; full-shadow = false; };
    dock = { shadow = false; }
    dnd = { shadow = false; }
    popup_menu = { opacity = 0.9; }
    dropdown_menu = { opacity = 0.9; }
};

# Corners
corner-radius = 12;
rounded-corners-exclude = [
    "window_type = 'dock'",
    "window_type = 'desktop'"
];

# Animations
animations = true;
animation-stiffness = 200;
animation-window-mass = 1;
animation-dampening = 20;
animation-clamping = false;

animation-for-open-window = "zoom";
animation-for-unmap-window = "zoom";
animation-for-transient-window = "slide-up";
animation-for-prev-tag = "minimize";
enable-fading-prev-tag = true;
enable-fading-next-tag = true;
