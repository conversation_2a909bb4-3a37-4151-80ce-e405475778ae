# 🌆 CYBERPUNK NEON i3 CONFIG 🌆
# i3 config file (v4) for X11 + NVIDIA

# Set mod key (Mod1=<Alt>, Mod4=<Super>)
set $mod Mod4

# Font for window titles and bar
font pango:JetBrains Mono Nerd Font 12

# 🎨 CYBERPUNK NEON COLORS 🎨
set $bg-color            #0a0a0f
set $inactive-bg-color   #1a0033
set $text-color          #00ffff
set $inactive-text-color #4d4d4d
set $urgent-bg-color     #ffff00
set $neon-pink           #ff00ff
set $neon-cyan           #00ffff
set $neon-green          #00ff41
set $neon-purple         #cc00ff

# Window colors
#                       border              background         text                 indicator
client.focused          $neon-pink          $neon-pink         $bg-color            $neon-cyan
client.focused_inactive $neon-cyan          $inactive-bg-color $inactive-text-color $neon-cyan
client.unfocused        $inactive-bg-color  $inactive-bg-color $inactive-text-color $neon-cyan
client.urgent           $urgent-bg-color    $urgent-bg-color   $bg-color            $neon-cyan

# Use Mouse+$mod to drag floating windows
floating_modifier $mod

# Start a terminal
bindsym $mod+Return exec kitty

# Kill focused window
bindsym $mod+Shift+q kill

# Start rofi (cyberpunk launcher)
bindsym $mod+d exec rofi -show drun -config ~/.config/rofi/config.rasi

# Change focus
bindsym $mod+j focus left
bindsym $mod+k focus down
bindsym $mod+l focus up
bindsym $mod+semicolon focus right

# Alternatively, you can use the cursor keys:
bindsym $mod+Left focus left
bindsym $mod+Down focus down
bindsym $mod+Up focus up
bindsym $mod+Right focus right

# Move focused window
bindsym $mod+Shift+j move left
bindsym $mod+Shift+k move down
bindsym $mod+Shift+l move up
bindsym $mod+Shift+semicolon move right

# Alternatively, you can use the cursor keys:
bindsym $mod+Shift+Left move left
bindsym $mod+Shift+Down move down
bindsym $mod+Shift+Up move up
bindsym $mod+Shift+Right move right

# Split in horizontal orientation
bindsym $mod+h split h

# Split in vertical orientation
bindsym $mod+v split v

# Enter fullscreen mode for the focused container
bindsym $mod+f fullscreen toggle

# Change container layout (stacked, tabbed, toggle split)
bindsym $mod+s layout stacking
bindsym $mod+w layout tabbed
bindsym $mod+e layout toggle split

# Toggle tiling / floating
bindsym $mod+Shift+space floating toggle

# Change focus between tiling / floating windows
bindsym $mod+space focus mode_toggle

# Focus the parent container
bindsym $mod+a focus parent

# Define names for default workspaces
set $ws1 "1:🔥"
set $ws2 "2:💻"
set $ws3 "3:🌐"
set $ws4 "4:📁"
set $ws5 "5:🎵"
set $ws6 "6:💬"
set $ws7 "7:🎮"
set $ws8 "8:📊"
set $ws9 "9:⚙️"
set $ws10 "10:🌆"

# Switch to workspace
bindsym $mod+1 workspace $ws1
bindsym $mod+2 workspace $ws2
bindsym $mod+3 workspace $ws3
bindsym $mod+4 workspace $ws4
bindsym $mod+5 workspace $ws5
bindsym $mod+6 workspace $ws6
bindsym $mod+7 workspace $ws7
bindsym $mod+8 workspace $ws8
bindsym $mod+9 workspace $ws9
bindsym $mod+0 workspace $ws10

# Move focused container to workspace
bindsym $mod+Shift+1 move container to workspace $ws1
bindsym $mod+Shift+2 move container to workspace $ws2
bindsym $mod+Shift+3 move container to workspace $ws3
bindsym $mod+Shift+4 move container to workspace $ws4
bindsym $mod+Shift+5 move container to workspace $ws5
bindsym $mod+Shift+6 move container to workspace $ws6
bindsym $mod+Shift+7 move container to workspace $ws7
bindsym $mod+Shift+8 move container to workspace $ws8
bindsym $mod+Shift+9 move container to workspace $ws9
bindsym $mod+Shift+0 move container to workspace $ws10

# Reload the configuration file
bindsym $mod+Shift+c reload

# Restart i3 inplace
bindsym $mod+Shift+r restart

# Exit i3
bindsym $mod+Shift+e exec "i3-nagbar -t warning -m 'You pressed the exit shortcut. Do you really want to exit i3? This will end your X session.' -B 'Yes, exit i3' 'i3-msg exit'"

# Resize window mode
mode "resize" {
        bindsym j resize shrink width 10 px or 10 ppt
        bindsym k resize grow height 10 px or 10 ppt
        bindsym l resize shrink height 10 px or 10 ppt
        bindsym semicolon resize grow width 10 px or 10 ppt

        bindsym Left resize shrink width 10 px or 10 ppt
        bindsym Down resize grow height 10 px or 10 ppt
        bindsym Up resize shrink height 10 px or 10 ppt
        bindsym Right resize grow width 10 px or 10 ppt

        bindsym Return mode "default"
        bindsym Escape mode "default"
        bindsym $mod+r mode "default"
}

bindsym $mod+r mode "resize"

# Window borders and gaps
for_window [class="^.*"] border pixel 3
gaps inner 15
gaps outer 10

# Floating windows
for_window [class="Rofi"] floating enable
for_window [class="feh"] floating enable

# 🚀 AUTOSTART APPLICATIONS 🚀
# Start compositor for transparency and effects
exec_always --no-startup-id picom --config ~/.config/picom/picom.conf

# Start polybar
exec_always --no-startup-id ~/.config/polybar/launch.sh

# Start dunst for notifications
exec --no-startup-id dunst

# Set wallpaper
exec_always --no-startup-id feh --bg-fill ~/.config/wallpapers/cyberpunk.jpg

# Network manager applet
exec --no-startup-id nm-applet

# Volume control
bindsym XF86AudioRaiseVolume exec --no-startup-id pactl set-sink-volume @DEFAULT_SINK@ +10%
bindsym XF86AudioLowerVolume exec --no-startup-id pactl set-sink-volume @DEFAULT_SINK@ -10%
bindsym XF86AudioMute exec --no-startup-id pactl set-sink-mute @DEFAULT_SINK@ toggle

# Brightness control
bindsym XF86MonBrightnessUp exec --no-startup-id brightnessctl set +10%
bindsym XF86MonBrightnessDown exec --no-startup-id brightnessctl set 10%-

# Screenshot
bindsym Print exec --no-startup-id flameshot gui

# Lock screen
bindsym $mod+shift+x exec i3lock -c 0a0a0f

# 🌆 CYBERPUNK POWER MENU 🌆
# Power menu with rofi
bindsym $mod+shift+p exec ~/.config/rofi/powermenu.sh

# Quick shortcuts
bindsym $mod+ctrl+l exec i3lock -c 0a0a0f
bindsym $mod+ctrl+r exec systemctl reboot
bindsym $mod+ctrl+s exec systemctl poweroff
