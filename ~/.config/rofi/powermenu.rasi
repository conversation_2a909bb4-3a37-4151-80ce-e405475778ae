/* 🌆 CYBERPUNK POWER MENU THEME 🌆 */

* {
    /* Cyberpunk Neon Colors */
    neon-pink: #ff00ff;
    neon-cyan: #00ffff;
    neon-green: #00ff41;
    neon-yellow: #ffff00;
    neon-red: #ff0080;
    dark-bg: #0a0a0f;
    dark-purple: #1a0033;
    
    background-color: transparent;
    text-color: @neon-cyan;
    font: "JetBrains Mono Nerd Font Bold 16";
}

window {
    transparency: "real";
    background-color: rgba(10, 10, 15, 0.98);
    border: 3px solid @neon-pink;
    border-radius: 20px;
    padding: 30px;
    width: 400px;
    location: center;
    anchor: center;
    box-shadow: 0 0 50px rgba(255, 0, 255, 0.9);
}

mainbox {
    border: 0;
    padding: 0;
    background-color: transparent;
    spacing: 20px;
}

message {
    border: 2px solid @neon-cyan;
    border-radius: 15px;
    padding: 15px;
    background-color: rgba(26, 0, 51, 0.9);
    text-color: @neon-cyan;
}

textbox {
    text-color: @neon-cyan;
    background-color: transparent;
    horizontal-align: 0.5;
}

listview {
    fixed-height: 0;
    border: 0;
    border-radius: 15px;
    background-color: transparent;
    padding: 10px;
    scrollbar: false;
    spacing: 10px;
}

element {
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 15px;
    margin: 5px;
    background-color: rgba(26, 0, 51, 0.7);
    text-color: @neon-cyan;
    horizontal-align: 0.5;
}

element normal.normal {
    background-color: rgba(26, 0, 51, 0.7);
    text-color: @neon-cyan;
}

element normal.urgent {
    background-color: @neon-red;
    text-color: @dark-bg;
}

element normal.active {
    background-color: @neon-green;
    text-color: @dark-bg;
}

element selected.normal {
    background-color: @neon-pink;
    text-color: @dark-bg;
    border: 2px solid @neon-cyan;
    box-shadow: 0 0 20px rgba(255, 0, 255, 1);
    transform: scale(1.05);
}

element selected.urgent {
    background-color: @neon-yellow;
    text-color: @dark-bg;
    box-shadow: 0 0 20px rgba(255, 255, 0, 1);
}

element selected.active {
    background-color: @neon-green;
    text-color: @dark-bg;
    box-shadow: 0 0 20px rgba(0, 255, 65, 1);
}

element-text {
    background-color: transparent;
    cursor: inherit;
    highlight: inherit;
    text-color: inherit;
    horizontal-align: 0.5;
}

inputbar {
    spacing: 0;
    text-color: @neon-cyan;
    padding: 15px;
    background-color: rgba(26, 0, 51, 0.9);
    border: 2px solid @neon-cyan;
    border-radius: 15px;
    margin: 0 0 20px 0;
}

prompt {
    spacing: 0;
    text-color: @neon-pink;
    background-color: transparent;
    margin: 0 10px 0 0;
    horizontal-align: 0.5;
}
