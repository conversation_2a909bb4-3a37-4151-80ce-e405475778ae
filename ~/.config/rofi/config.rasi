/* 🌆 CYBERPUNK NEON ROFI THEME 🌆 */

configuration {
    modi: "drun,run,window";
    width: 50;
    lines: 15;
    columns: 1;
    font: "JetBrains Mono Nerd Font 14";
    bw: 2;
    location: 0;
    padding: 20;
    yoffset: 0;
    xoffset: 0;
    fixed-num-lines: true;
    show-icons: true;
    terminal: "kitty";
    ssh-client: "ssh";
    ssh-command: "{terminal} -e {ssh-client} {host} [-p {port}]";
    run-command: "{cmd}";
    run-list-command: "";
    run-shell-command: "{terminal} -e {cmd}";
    window-command: "wmctrl -i -R {window}";
    window-match-fields: "all";
    icon-theme: "Oranchelo";
    drun-match-fields: "name,generic,exec,categories";
    drun-show-actions: false;
    drun-display-format: "{icon} {name}";
    disable-history: false;
    ignored-prefixes: "";
    sort: false;
    sorting-method: "normal";
    case-sensitive: false;
    cycle: true;
    sidebar-mode: false;
    eh: 1;
    auto-select: false;
    parse-hosts: false;
    parse-known-hosts: true;
    combi-modi: "window,run";
    matching: "fuzzy";
    tokenize: true;
    m: "-5";
    line-margin: 2;
    line-padding: 1;
    filter: "";
    separator-style: "dash";
    hide-scrollbar: true;
    fullscreen: false;
    fake-transparency: false;
    dpi: -1;
    threads: 0;
    scrollbar-width: 8;
    scroll-method: 0;
    fake-background: "screenshot";
    window-format: "{w}    {c}   {t}";
    click-to-exit: true;
    show-match: true;
    theme: "";
    color-normal: "";
    color-urgent: "";
    color-active: "";
    color-window: "";
    max-history-size: 25;
    combi-hide-mode-prefix: false;
    matching-negate-char: '-';
    cache-dir: "";
    pid: "/run/user/1000/rofi.pid";
    display-window: "";
    display-windowcd: "";
    display-run: "";
    display-ssh: "";
    display-drun: "";
    display-combi: "";
    display-keys: "";
    kb-primary-paste: "Control+V,Shift+Insert";
    kb-secondary-paste: "Control+v,Insert";
}

* {
    /* Cyberpunk Neon Colors */
    neon-pink: #ff00ff;
    neon-cyan: #00ffff;
    neon-green: #00ff41;
    neon-yellow: #ffff00;
    neon-blue: #0080ff;
    neon-purple: #cc00ff;
    dark-bg: #0a0a0f;
    dark-purple: #1a0033;
    
    background-color: transparent;
    text-color: @neon-cyan;
    font: "JetBrains Mono Nerd Font 14";
}

window {
    transparency: "real";
    background-color: rgba(10, 10, 15, 0.95);
    border: 2px solid @neon-pink;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 0 30px rgba(255, 0, 255, 0.8);
}

mainbox {
    border: 0;
    padding: 0;
    background-color: transparent;
}

message {
    border: 2px solid @neon-cyan;
    border-radius: 10px;
    padding: 10px;
    background-color: rgba(26, 0, 51, 0.8);
    text-color: @neon-cyan;
}

textbox {
    text-color: @neon-cyan;
    background-color: transparent;
}

listview {
    fixed-height: 0;
    border: 2px solid @neon-purple;
    border-radius: 10px;
    background-color: rgba(26, 0, 51, 0.6);
    padding: 10px;
    scrollbar: false;
}

element {
    border: 0;
    border-radius: 8px;
    padding: 8px;
    margin: 2px;
    background-color: transparent;
    text-color: @neon-cyan;
}

element normal.normal {
    background-color: transparent;
    text-color: @neon-cyan;
}

element normal.urgent {
    background-color: @neon-yellow;
    text-color: @dark-bg;
}

element normal.active {
    background-color: @neon-green;
    text-color: @dark-bg;
}

element selected.normal {
    background-color: @neon-pink;
    text-color: @dark-bg;
    border: 1px solid @neon-cyan;
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.8);
}

element selected.urgent {
    background-color: @neon-yellow;
    text-color: @dark-bg;
    box-shadow: 0 0 15px rgba(255, 255, 0, 0.8);
}

element selected.active {
    background-color: @neon-green;
    text-color: @dark-bg;
    box-shadow: 0 0 15px rgba(0, 255, 65, 0.8);
}

element-text {
    background-color: transparent;
    cursor: inherit;
    highlight: inherit;
    text-color: inherit;
}

element-icon {
    background-color: transparent;
    size: 24px;
    cursor: inherit;
    text-color: inherit;
}

scrollbar {
    width: 4px;
    border: 0;
    handle-color: @neon-pink;
    handle-width: 8px;
    padding: 0;
}

sidebar {
    border: 2px solid @neon-cyan;
    border-radius: 10px;
    background-color: rgba(26, 0, 51, 0.8);
}

button {
    spacing: 0;
    text-color: @neon-cyan;
    background-color: transparent;
    border: 1px solid @neon-cyan;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
}

button selected {
    background-color: @neon-pink;
    text-color: @dark-bg;
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.8);
}

inputbar {
    spacing: 0;
    text-color: @neon-cyan;
    padding: 10px;
    background-color: rgba(26, 0, 51, 0.8);
    border: 2px solid @neon-cyan;
    border-radius: 10px;
    margin: 0 0 10px 0;
}

case-indicator {
    spacing: 0;
    text-color: @neon-cyan;
    background-color: transparent;
}

entry {
    spacing: 0;
    text-color: @neon-cyan;
    background-color: transparent;
    placeholder-color: rgba(0, 255, 255, 0.5);
    placeholder: "🔍 Search...";
    cursor: text;
}

prompt {
    spacing: 0;
    text-color: @neon-pink;
    background-color: transparent;
    margin: 0 10px 0 0;
}

textbox-prompt-colon {
    expand: false;
    str: "🚀";
    margin: 0 5px 0 0;
    text-color: @neon-pink;
    background-color: transparent;
}
