#!/bin/bash

# 🌆 CYBERPUNK POWER MENU 🌆

# Options
lock="🔒 Lock"
logout="🚪 Logout"
reboot="🔄 Reboot"
shutdown="⚡ Shutdown"
cancel="❌ Cancel"

# Rofi CMD
rofi_cmd() {
    rofi -dmenu \
        -p "Power Menu" \
        -mesg "🌆 Choose your destiny..." \
        -theme ~/.config/rofi/powermenu.rasi
}

# Pass variables to rofi dmenu
run_rofi() {
    echo -e "$lock\n$logout\n$reboot\n$shutdown\n$cancel" | rofi_cmd
}

# Execute Command
run_cmd() {
    if [[ $1 == '--lock' ]]; then
        i3lock -c 0a0a0f
    elif [[ $1 == '--logout' ]]; then
        i3-msg exit
    elif [[ $1 == '--reboot' ]]; then
        systemctl reboot
    elif [[ $1 == '--shutdown' ]]; then
        systemctl poweroff
    fi
}

# Actions
chosen="$(run_rofi)"
case ${chosen} in
    $lock)
        run_cmd --lock
        ;;
    $logout)
        run_cmd --logout
        ;;
    $reboot)
        run_cmd --reboot
        ;;
    $shutdown)
        run_cmd --shutdown
        ;;
    $cancel)
        exit 0
        ;;
esac
