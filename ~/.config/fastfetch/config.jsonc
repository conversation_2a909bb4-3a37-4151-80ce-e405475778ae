{"$schema": "https://github.com/fastfetch-cli/fastfetch/raw/dev/doc/json_schema.json", "logo": {"source": "ubuntu", "padding": {"right": 1}}, "display": {"size": {"binaryPrefix": "si"}, "color": "blue", "separator": "  "}, "modules": ["title", "separator", "os", "host", "kernel", "uptime", "packages", "shell", "display", "de", "wm", "wmtheme", "theme", "icons", "font", "cursor", "terminal", "terminalfont", "cpu", "gpu", "memory", "swap", "disk", "localip", "battery", "poweradapter", "locale", "break", "colors"]}