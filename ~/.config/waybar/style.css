/* Tokyo Night Theme for Waybar */
* {
    border: none;
    border-radius: 0;
    font-family: "JetBrains Mono Nerd Font", "Fira Sans", sans-serif;
    font-size: 13px;
    min-height: 0;
}

window#waybar {
    background-color: rgba(26, 27, 38, 0.95);
    border-bottom: 3px solid rgba(122, 162, 247, 0.8);
    color: #c0caf5;
    transition-property: background-color;
    transition-duration: .5s;
}

window#waybar.hidden {
    opacity: 0.2;
}

button {
    box-shadow: inset 0 -3px transparent;
    border: none;
    border-radius: 0;
}

button:hover {
    background: inherit;
    box-shadow: inset 0 -3px #c0caf5;
}

#workspaces button {
    padding: 0 5px;
    background-color: transparent;
    color: #c0caf5;
}

#workspaces button:hover {
    background: rgba(122, 162, 247, 0.2);
}

#workspaces button.focused {
    background-color: #7aa2f7;
    color: #1a1b26;
    box-shadow: inset 0 -3px #c0caf5;
}

#workspaces button.urgent {
    background-color: #f7768e;
    color: #1a1b26;
}

#mode {
    background-color: #bb9af7;
    color: #1a1b26;
    border-bottom: 3px solid #c0caf5;
}

#clock,
#battery,
#cpu,
#memory,
#disk,
#temperature,
#backlight,
#network,
#pulseaudio,
#wireplumber,
#custom-media,
#tray,
#mode,
#idle_inhibitor,
#scratchpad,
#mpd {
    padding: 0 10px;
    color: #c0caf5;
}

#window,
#workspaces {
    margin: 0 4px;
}

.modules-left > widget:first-child > #workspaces {
    margin-left: 0;
}

.modules-right > widget:last-child > #workspaces {
    margin-right: 0;
}

#clock {
    background-color: #7aa2f7;
    color: #1a1b26;
    font-weight: bold;
}

#battery {
    background-color: #9ece6a;
    color: #1a1b26;
}

#battery.charging, #battery.plugged {
    background-color: #e0af68;
    color: #1a1b26;
}

@keyframes blink {
    to {
        background-color: #f7768e;
        color: #1a1b26;
    }
}

#battery.critical:not(.charging) {
    background-color: #f7768e;
    color: #1a1b26;
    animation-name: blink;
    animation-duration: 0.5s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-direction: alternate;
}

label:focus {
    background-color: #1a1b26;
}

#cpu {
    background-color: #bb9af7;
    color: #1a1b26;
}

#memory {
    background-color: #7dcfff;
    color: #1a1b26;
}

#disk {
    background-color: #e0af68;
    color: #1a1b26;
}

#backlight {
    background-color: #f7768e;
    color: #1a1b26;
}

#network {
    background-color: #9ece6a;
    color: #1a1b26;
}

#network.disconnected {
    background-color: #f7768e;
    color: #1a1b26;
}

#pulseaudio {
    background-color: #7aa2f7;
    color: #1a1b26;
}

#pulseaudio.muted {
    background-color: #414868;
    color: #c0caf5;
}

#wireplumber {
    background-color: #7aa2f7;
    color: #1a1b26;
}

#wireplayer.muted {
    background-color: #414868;
    color: #c0caf5;
}

#custom-media {
    background-color: #bb9af7;
    color: #1a1b26;
    min-width: 100px;
}

#custom-media.custom-spotify {
    background-color: #9ece6a;
    color: #1a1b26;
}

#custom-media.custom-vlc {
    background-color: #e0af68;
    color: #1a1b26;
}

#temperature {
    background-color: #7dcfff;
    color: #1a1b26;
}

#temperature.critical {
    background-color: #f7768e;
    color: #1a1b26;
}

#tray {
    background-color: #414868;
}

#tray > .passive {
    -gtk-icon-effect: dim;
}

#tray > .needs-attention {
    -gtk-icon-effect: highlight;
    background-color: #f7768e;
}

#idle_inhibitor {
    background-color: #414868;
}

#idle_inhibitor.activated {
    background-color: #e0af68;
    color: #1a1b26;
}

#mpd {
    background-color: #bb9af7;
    color: #1a1b26;
}

#mpd.disconnected {
    background-color: #414868;
    color: #c0caf5;
}

#mpd.stopped {
    background-color: #414868;
    color: #c0caf5;
}

#mpd.paused {
    background-color: #7dcfff;
    color: #1a1b26;
}

#language {
    background: #9ece6a;
    color: #1a1b26;
    padding: 0 5px;
    margin: 0 5px;
    min-width: 16px;
}

#keyboard-state {
    background: #e0af68;
    color: #1a1b26;
    padding: 0 0px;
    margin: 0 5px;
    min-width: 16px;
}

#keyboard-state > label {
    padding: 0 5px;
}

#keyboard-state > label.locked {
    background: rgba(0, 0, 0, 0.2);
}

#scratchpad {
    background: rgba(122, 162, 247, 0.2);
}

#scratchpad.empty {
    background-color: transparent;
}
