/* 🌆 CYBERPUNK NEON HACKER THEME 🌆 */
* {
    border: none;
    border-radius: 8px;
    font-family: "JetBrains Mono Nerd Font", monospace;
    font-size: 14px;
    font-weight: bold;
    min-height: 0;
    text-shadow: 0 0 5px currentColor;
}

window#waybar {
    background: linear-gradient(90deg, rgba(10, 10, 15, 0.9), rgba(26, 0, 51, 0.9));
    border-bottom: 2px solid #ff00ff;
    color: #00ffff;
    transition-property: background-color;
    transition-duration: .3s;
    box-shadow: 0 0 20px rgba(255, 0, 255, 0.5);
}

window#waybar.hidden {
    opacity: 0.3;
}

button {
    box-shadow: inset 0 -2px transparent;
    border: 1px solid rgba(255, 0, 255, 0.3);
    border-radius: 6px;
    margin: 2px;
    background: rgba(26, 0, 51, 0.6);
}

button:hover {
    background: rgba(255, 0, 255, 0.2);
    box-shadow: 0 0 10px rgba(255, 0, 255, 0.8);
    transform: scale(1.05);
    transition: all 0.2s ease;
}

#workspaces button {
    padding: 5px 10px;
    background: rgba(26, 0, 51, 0.8);
    color: #00ffff;
    border: 1px solid rgba(0, 255, 255, 0.3);
    margin: 2px;
}

#workspaces button:hover {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
}

#workspaces button.focused {
    background: linear-gradient(45deg, #ff00ff, #cc00ff);
    color: #000000;
    box-shadow: 0 0 20px rgba(255, 0, 255, 1);
    border: 1px solid #ff00ff;
}

#workspaces button.urgent {
    background: linear-gradient(45deg, #ffff00, #ff6600);
    color: #000000;
    animation: urgent-blink 1s infinite;
}

@keyframes urgent-blink {
    0%, 50% { box-shadow: 0 0 20px rgba(255, 255, 0, 1); }
    51%, 100% { box-shadow: 0 0 5px rgba(255, 255, 0, 0.3); }
}

#mode {
    background: linear-gradient(45deg, #cc00ff, #ff00ff);
    color: #000000;
    border-bottom: 2px solid #00ffff;
    box-shadow: 0 0 15px rgba(204, 0, 255, 0.8);
}

#clock,
#battery,
#cpu,
#memory,
#disk,
#temperature,
#backlight,
#network,
#pulseaudio,
#wireplumber,
#custom-media,
#tray,
#mode,
#idle_inhibitor,
#scratchpad,
#mpd {
    padding: 0 10px;
    color: #c0caf5;
}

#window,
#workspaces {
    margin: 0 4px;
}

.modules-left > widget:first-child > #workspaces {
    margin-left: 0;
}

.modules-right > widget:last-child > #workspaces {
    margin-right: 0;
}

#clock {
    background-color: #7aa2f7;
    color: #1a1b26;
    font-weight: bold;
}

#battery {
    background-color: #9ece6a;
    color: #1a1b26;
}

#battery.charging, #battery.plugged {
    background-color: #e0af68;
    color: #1a1b26;
}

@keyframes blink {
    to {
        background-color: #f7768e;
        color: #1a1b26;
    }
}

#battery.critical:not(.charging) {
    background-color: #f7768e;
    color: #1a1b26;
    animation-name: blink;
    animation-duration: 0.5s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-direction: alternate;
}

label:focus {
    background-color: #1a1b26;
}

#cpu {
    background-color: #bb9af7;
    color: #1a1b26;
}

#memory {
    background-color: #7dcfff;
    color: #1a1b26;
}

#disk {
    background-color: #e0af68;
    color: #1a1b26;
}

#backlight {
    background-color: #f7768e;
    color: #1a1b26;
}

#network {
    background-color: #9ece6a;
    color: #1a1b26;
}

#network.disconnected {
    background-color: #f7768e;
    color: #1a1b26;
}

#pulseaudio {
    background-color: #7aa2f7;
    color: #1a1b26;
}

#pulseaudio.muted {
    background-color: #414868;
    color: #c0caf5;
}

#wireplumber {
    background-color: #7aa2f7;
    color: #1a1b26;
}

#wireplayer.muted {
    background-color: #414868;
    color: #c0caf5;
}

#custom-media {
    background-color: #bb9af7;
    color: #1a1b26;
    min-width: 100px;
}

#custom-media.custom-spotify {
    background-color: #9ece6a;
    color: #1a1b26;
}

#custom-media.custom-vlc {
    background-color: #e0af68;
    color: #1a1b26;
}

#temperature {
    background-color: #7dcfff;
    color: #1a1b26;
}

#temperature.critical {
    background-color: #f7768e;
    color: #1a1b26;
}

#tray {
    background-color: #414868;
}

#tray > .passive {
    -gtk-icon-effect: dim;
}

#tray > .needs-attention {
    -gtk-icon-effect: highlight;
    background-color: #f7768e;
}

#idle_inhibitor {
    background-color: #414868;
}

#idle_inhibitor.activated {
    background-color: #e0af68;
    color: #1a1b26;
}

#mpd {
    background-color: #bb9af7;
    color: #1a1b26;
}

#mpd.disconnected {
    background-color: #414868;
    color: #c0caf5;
}

#mpd.stopped {
    background-color: #414868;
    color: #c0caf5;
}

#mpd.paused {
    background-color: #7dcfff;
    color: #1a1b26;
}

#language {
    background: #9ece6a;
    color: #1a1b26;
    padding: 0 5px;
    margin: 0 5px;
    min-width: 16px;
}

#keyboard-state {
    background: #e0af68;
    color: #1a1b26;
    padding: 0 0px;
    margin: 0 5px;
    min-width: 16px;
}

#keyboard-state > label {
    padding: 0 5px;
}

#keyboard-state > label.locked {
    background: rgba(0, 0, 0, 0.2);
}

#scratchpad {
    background: rgba(122, 162, 247, 0.2);
}

#scratchpad.empty {
    background-color: transparent;
}
