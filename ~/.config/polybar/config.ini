[colors]
; 🌆 CYBERPUNK NEON COLORS 🌆
background = #0a0a0f
background-alt = #1a0033
foreground = #00ffff
primary = #ff00ff
secondary = #cc00ff
alert = #ffff00
disabled = #4d4d4d
neon-green = #00ff41
neon-cyan = #00ffff
neon-pink = #ff00ff
neon-purple = #cc00ff

[bar/cyberpunk]
width = 100%
height = 35pt
radius = 12

; dpi = 96

background = ${colors.background}
foreground = ${colors.foreground}

line-size = 3pt

border-size = 2pt
border-color = ${colors.neon-pink}

padding-left = 1
padding-right = 1

module-margin = 1

separator = |
separator-foreground = ${colors.neon-cyan}

font-0 = "JetBrains Mono Nerd Font:size=12;2"
font-1 = "JetBrains Mono Nerd Font:size=16;3"

modules-left = xworkspaces xwindow
modules-right = filesystem pulseaudio xkeyboard memory cpu wlan eth date powermenu

cursor-click = pointer
cursor-scroll = ns-resize

enable-ipc = true

; wm-restack = generic
; wm-restack = bspwm
wm-restack = i3

; override-redirect = true

[module/systray]
type = internal/tray

format-margin = 8pt
tray-spacing = 16pt

[module/xworkspaces]
type = internal/xworkspaces

label-active = %name%
label-active-background = ${colors.neon-pink}
label-active-foreground = ${colors.background}
label-active-underline= ${colors.neon-cyan}
label-active-padding = 1

label-occupied = %name%
label-occupied-padding = 1
label-occupied-foreground = ${colors.neon-cyan}

label-urgent = %name%
label-urgent-background = ${colors.alert}
label-urgent-foreground = ${colors.background}
label-urgent-padding = 1

label-empty = %name%
label-empty-foreground = ${colors.disabled}
label-empty-padding = 1

[module/xwindow]
type = internal/xwindow
label = %title:0:60:...%
label-foreground = ${colors.neon-green}

[module/filesystem]
type = internal/fs
interval = 25

mount-0 = /

label-mounted = 💾 %{F#00ff41}%mountpoint%%{F-} %percentage_used%%

label-unmounted = %mountpoint% not mounted
label-unmounted-foreground = ${colors.disabled}

[module/pulseaudio]
type = internal/pulseaudio

format-volume-prefix = "🔊 "
format-volume-prefix-foreground = ${colors.neon-cyan}
format-volume = <label-volume>

label-volume = %percentage%%
label-volume-foreground = ${colors.neon-cyan}

label-muted = 🔇 muted
label-muted-foreground = ${colors.disabled}

[module/xkeyboard]
type = internal/xkeyboard
blacklist-0 = num lock

label-layout = ⌨️ %layout%
label-layout-foreground = ${colors.neon-purple}

label-indicator-padding = 2
label-indicator-margin = 1
label-indicator-foreground = ${colors.background}
label-indicator-background = ${colors.neon-pink}

[module/memory]
type = internal/memory
interval = 2
format-prefix = "🧠 "
format-prefix-foreground = ${colors.neon-green}
label = %percentage_used:2%%

[module/cpu]
type = internal/cpu
interval = 2
format-prefix = "⚡ "
format-prefix-foreground = ${colors.neon-pink}
label = %percentage:2%%

[network-base]
type = internal/network
interval = 5
format-connected = <label-connected>
format-disconnected = <label-disconnected>
label-disconnected = %{F#ff0080}%ifname%%{F#4d4d4d} disconnected

[module/wlan]
inherit = network-base
interface-type = wireless
label-connected = 📶 %{F#00ffff}%ifname%%{F-} %essid% %local_ip%

[module/eth]
inherit = network-base
interface-type = wired
label-connected = 🌐 %{F#00ffff}%ifname%%{F-} %local_ip%

[module/date]
type = internal/date
interval = 1

date = %H:%M
date-alt = %Y-%m-%d %H:%M:%S

label = 🕐 %date%
label-foreground = ${colors.neon-cyan}

[module/powermenu]
type = custom/menu

expand-right = true

format-spacing = 1

label-open = ⚡
label-open-foreground = ${colors.neon-pink}
label-close = ✕
label-close-foreground = ${colors.neon-pink}
label-separator = |
label-separator-foreground = ${colors.neon-cyan}

menu-0-0 = 🔒 Lock
menu-0-0-exec = i3lock -c 0a0a0f
menu-0-1 = 🚪 Logout
menu-0-1-exec = i3-msg exit
menu-0-2 = 🔄 Reboot
menu-0-2-exec = systemctl reboot
menu-0-3 = ⚡ Shutdown
menu-0-3-exec = systemctl poweroff

[settings]
screenchange-reload = true
pseudo-transparency = true

; vim:ft=dosini
