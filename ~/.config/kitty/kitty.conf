# Tokyo Night Theme for Kitty Terminal

# Font configuration
font_family      JetBrains Mono Nerd Font
bold_font        auto
italic_font      auto
bold_italic_font auto
font_size 12.0

# Cursor
cursor_shape block
cursor_blink_interval 0.5
cursor_stop_blinking_after 15.0

# Scrollback
scrollback_lines 2000
scrollback_pager less --chop-long-lines --RAW-CONTROL-CHARS +INPUT_LINE_NUMBER
scrollback_pager_history_size 0
scrollback_fill_enlarged_window no
wheel_scroll_multiplier 5.0
touch_scroll_multiplier 1.0

# Mouse
mouse_hide_wait 3.0
url_color #7aa2f7
url_style curly
open_url_with default
url_prefixes http https file ftp gemini irc gopher mailto news git
detect_urls yes
copy_on_select no
strip_trailing_spaces never
select_by_word_characters @-./_~?&=%+#
click_interval -1.0
focus_follows_mouse no
pointer_shape_when_grabbed arrow
default_pointer_shape beam
pointer_shape_when_dragging beam

# Performance tuning
repaint_delay 10
input_delay 3
sync_to_monitor yes

# Terminal bell
enable_audio_bell no
visual_bell_duration 0.0
window_alert_on_bell yes
bell_on_tab yes
command_on_bell none

# Window layout
remember_window_size  yes
initial_window_width  640
initial_window_height 400
enabled_layouts *
window_resize_step_cells 2
window_resize_step_lines 2
window_border_width 0.5pt
draw_minimal_borders yes
window_margin_width 0
single_window_margin_width -1
window_padding_width 0
placement_strategy center
active_border_color #7aa2f7
inactive_border_color #414868
bell_border_color #f7768e
inactive_text_alpha 1.0
hide_window_decorations no
resize_debounce_time 0.1
resize_draw_strategy static
resize_in_steps no
confirm_os_window_close 0

# Tab bar
tab_bar_edge bottom
tab_bar_margin_width 0.0
tab_bar_margin_height 0.0 0.0
tab_bar_style powerline
tab_bar_min_tabs 2
tab_switch_strategy previous
tab_fade 0.25 0.5 0.75 1
tab_separator " ┇"
tab_powerline_style angled
tab_activity_symbol none
tab_title_template "{title}"
active_tab_title_template none
active_tab_foreground   #1a1b26
active_tab_background   #7aa2f7
active_tab_font_style   bold-italic
inactive_tab_foreground #c0caf5
inactive_tab_background #414868
inactive_tab_font_style normal

# Color scheme - Tokyo Night
foreground #c0caf5
background #1a1b26
selection_foreground #c0caf5
selection_background #33467c

# Cursor colors
cursor #c0caf5
cursor_text_color #1a1b26

# URL underline color when hovering with mouse
url_color #7aa2f7

# Kitty window border colors
active_border_color #7aa2f7
inactive_border_color #414868
bell_border_color #f7768e

# OS Window titlebar colors
wayland_titlebar_color system
macos_titlebar_color system

# Tab bar colors
active_tab_foreground   #1a1b26
active_tab_background   #7aa2f7
inactive_tab_foreground #c0caf5
inactive_tab_background #414868
tab_bar_background      #1a1b26

# Colors for marks (marked text in the terminal)
mark1_foreground #1a1b26
mark1_background #7aa2f7
mark2_foreground #1a1b26
mark2_background #bb9af7
mark3_foreground #1a1b26
mark3_background #7dcfff

# The 16 terminal colors

# normal
color0 #15161e
color1 #f7768e
color2 #9ece6a
color3 #e0af68
color4 #7aa2f7
color5 #bb9af7
color6 #7dcfff
color7 #a9b1d6

# bright
color8 #414868
color9 #f7768e
color10 #9ece6a
color11 #e0af68
color12 #7aa2f7
color13 #bb9af7
color14 #7dcfff
color15 #c0caf5

# Extended colors
color16 #ff9e64
color17 #db4b4b

# Key mappings
map ctrl+shift+c copy_to_clipboard
map ctrl+shift+v paste_from_clipboard
map ctrl+shift+s paste_from_selection
map shift+insert paste_from_selection
map ctrl+shift+o pass_selection_to_program

# Window management
map ctrl+shift+enter new_window
map ctrl+shift+n new_os_window
map ctrl+shift+w close_window
map ctrl+shift+] next_window
map ctrl+shift+[ previous_window
map ctrl+shift+f move_window_forward
map ctrl+shift+b move_window_backward
map ctrl+shift+` move_window_to_top
map ctrl+shift+r start_resizing_window
map ctrl+shift+1 first_window
map ctrl+shift+2 second_window
map ctrl+shift+3 third_window
map ctrl+shift+4 fourth_window
map ctrl+shift+5 fifth_window
map ctrl+shift+6 sixth_window
map ctrl+shift+7 seventh_window
map ctrl+shift+8 eighth_window
map ctrl+shift+9 ninth_window
map ctrl+shift+0 tenth_window

# Tab management
map ctrl+shift+right next_tab
map ctrl+shift+left previous_tab
map ctrl+shift+t new_tab
map ctrl+shift+q close_tab
map ctrl+shift+. move_tab_forward
map ctrl+shift+, move_tab_backward
map ctrl+shift+alt+t set_tab_title

# Layout management
map ctrl+shift+l next_layout

# Font sizes
map ctrl+shift+equal change_font_size all +2.0
map ctrl+shift+plus change_font_size all +2.0
map ctrl+shift+kp_add change_font_size all +2.0
map ctrl+shift+minus change_font_size all -2.0
map ctrl+shift+kp_subtract change_font_size all -2.0
map ctrl+shift+backspace change_font_size all 0

# Select and act on visible text
map ctrl+shift+e kitten hints
map ctrl+shift+p>f kitten hints --type path --program -
map ctrl+shift+p>shift+f kitten hints --type path
map ctrl+shift+p>l kitten hints --type line --program -
map ctrl+shift+p>w kitten hints --type word --program -
map ctrl+shift+p>h kitten hints --type hash --program -
map ctrl+shift+p>n kitten hints --type linenum

# Miscellaneous
map ctrl+shift+f11 toggle_fullscreen
map ctrl+shift+f10 toggle_maximized
map ctrl+shift+u kitten unicode_input
map ctrl+shift+f2 edit_config_file
map ctrl+shift+escape kitty_shell window

# Sending arbitrary text on key presses
map ctrl+alt+enter send_text all \x1b[13;7u
map ctrl+alt+a send_text all Special text
map ctrl+shift+delete clear_terminal reset active

# You can open a new window running an arbitrary program
map ctrl+shift+y new_window less @selection

# You can open a new window with the current working directory set to the
# working directory of the current window.
map ctrl+alt+enter new_window_with_cwd

# You can open a new tab with the current working directory set to the
# working directory of the current window.
map ctrl+alt+t new_tab_with_cwd
