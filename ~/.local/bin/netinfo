#!/bin/bash

# 🌆 CANCROSOFTM NETWORK INFO 🌆

NEON_PINK='\033[38;5;201m'
NEON_CYAN='\033[38;5;51m'
NEON_GREEN='\033[38;5;46m'
NEON_YELLOW='\033[38;5;226m'
RESET='\033[0m'

echo -e "${NEON_PINK}🔥 CANCROSOFTM NETWORK ANALYSIS 🔥${RESET}"
echo ""

# Network interfaces
echo -e "${NEON_CYAN}🌐 NETWORK INTERFACES:${RESET}"
ip addr show | grep -E "^[0-9]|inet " | while read line; do
    if [[ $line =~ ^[0-9] ]]; then
        interface=$(echo $line | awk '{print $2}' | sed 's/://')
        echo -e "${NEON_YELLOW}Interface: $interface${RESET}"
    elif [[ $line =~ inet ]]; then
        ip=$(echo $line | awk '{print $2}')
        echo -e "${NEON_GREEN}  IP: $ip${RESET}"
    fi
done

echo ""
echo -e "${NEON_CYAN}🚪 GATEWAY & DNS:${RESET}"
gateway=$(ip route | grep default | awk '{print $3}' | head -1)
echo -e "${NEON_GREEN}Gateway: $gateway${RESET}"

dns=$(systemd-resolve --status | grep "DNS Servers" | head -1 | awk '{print $3}')
echo -e "${NEON_GREEN}DNS: $dns${RESET}"

# Public IP
echo ""
echo -e "${NEON_CYAN}🌍 PUBLIC IP:${RESET}"
public_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unable to fetch")
echo -e "${NEON_GREEN}$public_ip${RESET}"

# Network speed test
echo ""
echo -e "${NEON_CYAN}📡 NETWORK CONNECTIVITY:${RESET}"
if ping -c 1 ******* > /dev/null 2>&1; then
    echo -e "${NEON_GREEN}✅ Internet: Connected${RESET}"
    ping_time=$(ping -c 1 ******* | grep "time=" | awk -F'time=' '{print $2}' | awk '{print $1}')
    echo -e "${NEON_GREEN}Ping to *******: $ping_time${RESET}"
else
    echo -e "${NEON_RED}❌ Internet: Disconnected${RESET}"
fi

# Active connections
echo ""
echo -e "${NEON_CYAN}🔗 ACTIVE CONNECTIONS:${RESET}"
netstat -tuln 2>/dev/null | grep LISTEN | head -5 | while read line; do
    port=$(echo $line | awk '{print $4}' | awk -F':' '{print $NF}')
    protocol=$(echo $line | awk '{print $1}')
    echo -e "${NEON_GREEN}$protocol port $port${RESET}"
done

echo ""
echo -e "${NEON_YELLOW}💡 Use 'ss -tuln' for detailed connection info${RESET}"
