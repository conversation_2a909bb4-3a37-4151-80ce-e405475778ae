#!/bin/bash

# 🌆 CANCROSOFTM SYSTEM INFO 🌆

NEON_PINK='\033[38;5;201m'
NEON_CYAN='\033[38;5;51m'
NEON_GREEN='\033[38;5;46m'
NEON_YELLOW='\033[38;5;226m'
RESET='\033[0m'

echo -e "${NEON_PINK}🔥 CANCROSOFTM SYSTEM ANALYSIS 🔥${RESET}"
echo ""

# CPU Info
echo -e "${NEON_CYAN}⚡ CPU:${RESET}"
echo -e "${NEON_GREEN}$(lscpu | grep 'Model name' | cut -d':' -f2 | xargs)${RESET}"
echo -e "${NEON_YELLOW}Cores: $(nproc) | Threads: $(lscpu | grep '^CPU(s):' | awk '{print $2}')${RESET}"

# Memory Info
echo ""
echo -e "${NEON_CYAN}🧠 MEMORY:${RESET}"
free -h | awk 'NR==2{printf "'"${NEON_GREEN}"'Used: %s | Free: %s | Total: %s'"${RESET}"'\n", $3,$7,$2}'

# Disk Info
echo ""
echo -e "${NEON_CYAN}💾 STORAGE:${RESET}"
df -h / | awk 'NR==2{printf "'"${NEON_GREEN}"'Used: %s | Available: %s | Total: %s'"${RESET}"'\n", $3,$4,$2}'

# GPU Info
echo ""
echo -e "${NEON_CYAN}🎮 GPU:${RESET}"
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=name,memory.total,memory.used --format=csv,noheader,nounits | while read line; do
        echo -e "${NEON_GREEN}$line${RESET}"
    done
else
    lspci | grep -i vga | cut -d':' -f3 | sed 's/^[ \t]*//' | head -1 | xargs -I {} echo -e "${NEON_GREEN}{}${RESET}"
fi

# Network Info
echo ""
echo -e "${NEON_CYAN}🌐 NETWORK:${RESET}"
ip route | grep default | awk '{print $3}' | head -1 | xargs -I {} echo -e "${NEON_GREEN}Gateway: {}${RESET}"
hostname -I | awk '{print $1}' | xargs -I {} echo -e "${NEON_GREEN}Local IP: {}${RESET}"

# Uptime
echo ""
echo -e "${NEON_CYAN}⏰ UPTIME:${RESET}"
echo -e "${NEON_GREEN}$(uptime -p)${RESET}"

# Temperature (if available)
echo ""
echo -e "${NEON_CYAN}🌡️ TEMPERATURE:${RESET}"
if command -v sensors &> /dev/null; then
    sensors | grep -E "(Core|temp)" | head -3 | while read line; do
        echo -e "${NEON_GREEN}$line${RESET}"
    done
else
    echo -e "${NEON_YELLOW}Install lm-sensors for temperature monitoring${RESET}"
fi
