#!/bin/bash

# 🌆 CANCROSOFTM NEON BANNER 🌆

# Colors
NEON_PINK='\033[38;5;201m'
NEON_CYAN='\033[38;5;51m'
NEON_GREEN='\033[38;5;46m'
NEON_YELLOW='\033[38;5;226m'
NEON_PURPLE='\033[38;5;129m'
RESET='\033[0m'

# Clear screen
clear

# Custom ASCII Art from art.txt
if [ -f ~/Documents/augment-projects/InicioUbuntu/art.txt ]; then
    echo -e "${NEON_PINK}"
    cat ~/Documents/augment-projects/InicioUbuntu/art.txt | lolcat -a -d 2
    echo -e "${RESET}"
elif [ -f ~/art.txt ]; then
    echo -e "${NEON_PINK}"
    cat ~/art.txt | lolcat -a -d 2
    echo -e "${RESET}"
else
    # Fallback ASCII
    echo -e "${NEON_PINK}"
    figlet -f slant "CANCROSOFTM" | lolcat -a -d 2
    echo -e "${RESET}"
fi

echo -e "${NEON_GREEN}╔══════════════════════════════════════════════════════════════╗${RESET}"
echo -e "${NEON_GREEN}║${NEON_PINK}                    🌆 CANCROSOFTM TERMINAL 🌆                  ${NEON_GREEN}║${RESET}"
echo -e "${NEON_GREEN}║${NEON_CYAN}                   Entre a luz e as trevas!                   ${NEON_GREEN}║${RESET}"
echo -e "${NEON_GREEN}╚══════════════════════════════════════════════════════════════╝${RESET}"

echo ""
echo -e "${NEON_YELLOW}⚡ System Status:${RESET}"
echo -e "${NEON_CYAN}├─ User: ${NEON_PINK}$(whoami)${RESET}"
echo -e "${NEON_CYAN}├─ Host: ${NEON_PINK}$(hostname)${RESET}"
echo -e "${NEON_CYAN}├─ Uptime: ${NEON_PINK}$(uptime -p)${RESET}"
echo -e "${NEON_CYAN}└─ Date: ${NEON_PINK}$(date '+%Y-%m-%d %H:%M:%S')${RESET}"

echo ""
echo -e "${NEON_PURPLE}🔥 CANCROSOFTM Commands:${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}sysinfo${NEON_CYAN}    - System information${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}procmon${NEON_CYAN}    - Process monitor${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}netinfo${NEON_CYAN}    - Network analysis${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}cleanup${NEON_CYAN}    - System cleanup${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}btop${NEON_CYAN}       - Interactive monitor${RESET}"
echo -e "${NEON_CYAN}└─ ${NEON_GREEN}matrix${NEON_CYAN}     - Enter the Matrix${RESET}"

echo ""
