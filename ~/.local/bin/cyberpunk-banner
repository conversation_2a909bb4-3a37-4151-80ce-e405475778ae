#!/bin/bash

# 🌆 CYBERPUNK NEON BANNER 🌆

# Colors
NEON_PINK='\033[38;5;201m'
NEON_CYAN='\033[38;5;51m'
NEON_GREEN='\033[38;5;46m'
NEON_YELLOW='\033[38;5;226m'
NEON_PURPLE='\033[38;5;129m'
RESET='\033[0m'

# Clear screen
clear

# Cyberpunk ASCII Art
echo -e "${NEON_PINK}"
figlet -f slant "CYBER" | lolcat -a -d 2
echo -e "${NEON_CYAN}"
figlet -f slant "PUNK" | lolcat -a -d 2

echo -e "${NEON_GREEN}╔══════════════════════════════════════════════════════════════╗${RESET}"
echo -e "${NEON_GREEN}║${NEON_PINK}                    🌆 NEON HACKER TERMINAL 🌆                  ${NEON_GREEN}║${RESET}"
echo -e "${NEON_GREEN}║${NEON_CYAN}                     Welcome to the Matrix                     ${NEON_GREEN}║${RESET}"
echo -e "${NEON_GREEN}╚══════════════════════════════════════════════════════════════╝${RESET}"

echo ""
echo -e "${NEON_YELLOW}⚡ System Status:${RESET}"
echo -e "${NEON_CYAN}├─ User: ${NEON_PINK}$(whoami)${RESET}"
echo -e "${NEON_CYAN}├─ Host: ${NEON_PINK}$(hostname)${RESET}"
echo -e "${NEON_CYAN}├─ Uptime: ${NEON_PINK}$(uptime -p)${RESET}"
echo -e "${NEON_CYAN}└─ Date: ${NEON_PINK}$(date '+%Y-%m-%d %H:%M:%S')${RESET}"

echo ""
echo -e "${NEON_PURPLE}🔥 Available Commands:${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}matrix${NEON_CYAN}     - Enter the Matrix${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}hack${NEON_CYAN}       - Hacker simulation${RESET}"
echo -e "${NEON_CYAN}├─ ${NEON_GREEN}neon${NEON_CYAN}       - Neon system info${RESET}"
echo -e "${NEON_CYAN}└─ ${NEON_GREEN}cyber${NEON_CYAN}      - Cyberpunk tools${RESET}"

echo ""
