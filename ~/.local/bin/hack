#!/bin/bash

# Hacker simulation script
NEON_GREEN='\033[38;5;46m'
NEON_RED='\033[38;5;196m'
NEON_CYAN='\033[38;5;51m'
RESET='\033[0m'

echo -e "${NEON_GREEN}🔥 INITIATING HACK SEQUENCE...${RESET}"
sleep 1

echo -e "${NEON_CYAN}[+] Scanning network...${RESET}"
for i in {1..10}; do
    echo -e "${NEON_GREEN}[+] Found target: 192.168.1.$((RANDOM % 255))${RESET}"
    sleep 0.2
done

echo -e "${NEON_CYAN}[+] Exploiting vulnerabilities...${RESET}"
for i in {1..5}; do
    echo -e "${NEON_GREEN}[+] Buffer overflow detected on port $((RANDOM % 9999))${RESET}"
    sleep 0.3
done

echo -e "${NEON_RED}[!] ACCESS GRANTED${RESET}"
echo -e "${NEON_GREEN}[+] Welcome to the mainframe${RESET}"

# Show some fake system info
echo ""
echo -e "${NEON_CYAN}=== SYSTEM COMPROMISED ===${RESET}"
echo -e "${NEON_GREEN}CPU: $(lscpu | grep 'Model name' | cut -d':' -f2 | xargs)${RESET}"
echo -e "${NEON_GREEN}RAM: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')${RESET}"
echo -e "${NEON_GREEN}DISK: $(df -h / | tail -1 | awk '{print $3 "/" $2}')${RESET}"
