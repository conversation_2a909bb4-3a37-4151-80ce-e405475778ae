#!/bin/bash

# 🌆 CANCROSOFTM PROCESS MONITOR 🌆

NEON_PINK='\033[38;5;201m'
NEON_CYAN='\033[38;5;51m'
NEON_GREEN='\033[38;5;46m'
NEON_YELLOW='\033[38;5;226m'
RESET='\033[0m'

echo -e "${NEON_PINK}🔥 CANCROSOFTM PROCESS MONITOR 🔥${RESET}"
echo ""

echo -e "${NEON_CYAN}📊 TOP CPU PROCESSES:${RESET}"
ps aux --sort=-%cpu | head -6 | awk 'NR>1{printf "'"${NEON_GREEN}"'%s: %.1f%% CPU | %.1f%% MEM'"${RESET}"'\n", $11, $3, $4}'

echo ""
echo -e "${NEON_CYAN}🧠 TOP MEMORY PROCESSES:${RESET}"
ps aux --sort=-%mem | head -6 | awk 'NR>1{printf "'"${NEON_GREEN}"'%s: %.1f%% MEM | %.1f%% CPU'"${RESET}"'\n", $11, $4, $3}'

echo ""
echo -e "${NEON_CYAN}⚡ SYSTEM LOAD:${RESET}"
uptime | awk -F'load average:' '{print $2}' | xargs -I {} echo -e "${NEON_GREEN}Load Average: {}${RESET}"

echo ""
echo -e "${NEON_CYAN}🔢 PROCESS COUNT:${RESET}"
echo -e "${NEON_GREEN}Total Processes: $(ps aux | wc -l)${RESET}"
echo -e "${NEON_GREEN}Running: $(ps aux | awk '$8 ~ /^R/ {count++} END {print count+0}')${RESET}"
echo -e "${NEON_GREEN}Sleeping: $(ps aux | awk '$8 ~ /^S/ {count++} END {print count+0}')${RESET}"

echo ""
echo -e "${NEON_YELLOW}💡 Use 'btop' for interactive monitoring${RESET}"
