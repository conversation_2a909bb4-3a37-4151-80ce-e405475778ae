#!/bin/bash

# 🌆 CANCROSOFTM SYSTEM CLEANUP 🌆

NEON_PINK='\033[38;5;201m'
NEON_CYAN='\033[38;5;51m'
NEON_GREEN='\033[38;5;46m'
NEON_YELLOW='\033[38;5;226m'
NEON_RED='\033[38;5;196m'
RESET='\033[0m'

echo -e "${NEON_PINK}🔥 CANCROSOFTM SYSTEM CLEANUP 🔥${RESET}"
echo ""

# Check disk space before
echo -e "${NEON_CYAN}📊 DISK SPACE BEFORE CLEANUP:${RESET}"
df -h / | awk 'NR==2{printf "'"${NEON_YELLOW}"'Used: %s | Available: %s | Usage: %s'"${RESET}"'\n", $3,$4,$5}'

echo ""
echo -e "${NEON_CYAN}🧹 STARTING CLEANUP...${RESET}"

# APT cleanup
echo -e "${NEON_GREEN}[1/6] Cleaning APT cache...${RESET}"
sudo apt autoremove -y > /dev/null 2>&1
sudo apt autoclean > /dev/null 2>&1

# Flatpak cleanup
echo -e "${NEON_GREEN}[2/6] Cleaning Flatpak cache...${RESET}"
flatpak uninstall --unused -y > /dev/null 2>&1

# Snap cleanup
echo -e "${NEON_GREEN}[3/6] Cleaning Snap cache...${RESET}"
sudo snap refresh > /dev/null 2>&1

# Temp files
echo -e "${NEON_GREEN}[4/6] Cleaning temporary files...${RESET}"
sudo rm -rf /tmp/* > /dev/null 2>&1
rm -rf ~/.cache/thumbnails/* > /dev/null 2>&1

# Log files
echo -e "${NEON_GREEN}[5/6] Cleaning old logs...${RESET}"
sudo journalctl --vacuum-time=7d > /dev/null 2>&1

# User cache
echo -e "${NEON_GREEN}[6/6] Cleaning user cache...${RESET}"
rm -rf ~/.cache/mozilla/* > /dev/null 2>&1
rm -rf ~/.cache/google-chrome/* > /dev/null 2>&1

echo ""
echo -e "${NEON_CYAN}📊 DISK SPACE AFTER CLEANUP:${RESET}"
df -h / | awk 'NR==2{printf "'"${NEON_GREEN}"'Used: %s | Available: %s | Usage: %s'"${RESET}"'\n", $3,$4,$5}'

echo ""
echo -e "${NEON_PINK}✅ CLEANUP COMPLETED!${RESET}"
