#!/bin/bash

echo "🌆 APLICANDO TEMA CYBERPUNK NEON HACKER 🌆"

# Aplicar configurações GTK com cores cyberpunk
echo "📱 Configurando GTK com tema escuro..."
gsettings set org.gnome.desktop.interface gtk-theme 'Breeze-Dark'
gsettings set org.gnome.desktop.interface icon-theme 'breeze-dark'
gsettings set org.gnome.desktop.interface cursor-theme 'Layan-cursors'
gsettings set org.gnome.desktop.interface font-name 'JetBrains Mono Nerd Font 11'
gsettings set org.gnome.desktop.interface monospace-font-name 'JetBrains Mono Nerd Font 13'
gsettings set org.gnome.desktop.interface color-scheme 'prefer-dark'

# Configurar wallpaper cyberpunk (cor sólida escura)
echo "🖼️ Configurando wallpaper cyberpunk..."
gsettings set org.gnome.desktop.background picture-uri ''
gsettings set org.gnome.desktop.background picture-uri-dark ''
gsettings set org.gnome.desktop.background primary-color '#0a0a0f'
gsettings set org.gnome.desktop.background color-shading-type 'solid'

# Configurar terminal padrão
echo "💻 Configurando terminal padrão..."
gsettings set org.gnome.desktop.default-applications.terminal exec 'kitty'

# Recarregar cache de fontes
echo "🔤 Recarregando cache de fontes..."
fc-cache -fv > /dev/null 2>&1

# Criar scripts cyberpunk
echo "🔥 Criando scripts cyberpunk..."

# Script matrix
cat > ~/.local/bin/matrix << 'EOF'
#!/bin/bash
echo "🌆 Entering the Matrix..."
sleep 1
cmatrix -s -C cyan
EOF

# Script hack
cat > ~/.local/bin/hack << 'EOF'
#!/bin/bash
NEON_GREEN='\033[38;5;46m'
NEON_RED='\033[38;5;196m'
NEON_CYAN='\033[38;5;51m'
RESET='\033[0m'

echo -e "${NEON_GREEN}🔥 INITIATING HACK SEQUENCE...${RESET}"
sleep 1

echo -e "${NEON_CYAN}[+] Scanning network...${RESET}"
for i in {1..10}; do
    echo -e "${NEON_GREEN}[+] Found target: 192.168.1.$((RANDOM % 255))${RESET}"
    sleep 0.2
done

echo -e "${NEON_CYAN}[+] Exploiting vulnerabilities...${RESET}"
for i in {1..5}; do
    echo -e "${NEON_GREEN}[+] Buffer overflow detected on port $((RANDOM % 9999))${RESET}"
    sleep 0.3
done

echo -e "${NEON_RED}[!] ACCESS GRANTED${RESET}"
echo -e "${NEON_GREEN}[+] Welcome to the mainframe${RESET}"

echo ""
echo -e "${NEON_CYAN}=== SYSTEM COMPROMISED ===${RESET}"
echo -e "${NEON_GREEN}CPU: $(lscpu | grep 'Model name' | cut -d':' -f2 | xargs)${RESET}"
echo -e "${NEON_GREEN}RAM: $(free -h | grep '^Mem:' | awk '{print $3 "/" $2}')${RESET}"
echo -e "${NEON_GREEN}DISK: $(df -h / | tail -1 | awk '{print $3 "/" $2}')${RESET}"
EOF

# Script neon
cat > ~/.local/bin/neon << 'EOF'
#!/bin/bash
echo "🌆 NEON SYSTEM SCAN" | lolcat -a -d 2
echo ""
neofetch --ascii_distro ubuntu --colors 5 1 6 2 3 4
EOF

# Script cyber (menu de ferramentas)
cat > ~/.local/bin/cyber << 'EOF'
#!/bin/bash
NEON_PINK='\033[38;5;201m'
NEON_CYAN='\033[38;5;51m'
NEON_GREEN='\033[38;5;46m'
RESET='\033[0m'

clear
echo -e "${NEON_PINK}🌆 CYBERPUNK TOOLS MENU 🌆${RESET}"
echo ""
echo -e "${NEON_CYAN}1) ${NEON_GREEN}matrix${NEON_CYAN}  - Enter the Matrix${RESET}"
echo -e "${NEON_CYAN}2) ${NEON_GREEN}hack${NEON_CYAN}    - Hacker simulation${RESET}"
echo -e "${NEON_CYAN}3) ${NEON_GREEN}neon${NEON_CYAN}    - System info${RESET}"
echo -e "${NEON_CYAN}4) ${NEON_GREEN}btop${NEON_CYAN}    - System monitor${RESET}"
echo -e "${NEON_CYAN}5) ${NEON_GREEN}exit${NEON_CYAN}    - Exit${RESET}"
echo ""
echo -ne "${NEON_PINK}Choose option: ${RESET}"
read choice

case $choice in
    1) matrix ;;
    2) hack ;;
    3) neon ;;
    4) btop ;;
    5) exit ;;
    *) echo -e "${NEON_PINK}Invalid option${RESET}" ;;
esac
EOF

# Tornar scripts executáveis
chmod +x ~/.local/bin/matrix ~/.local/bin/hack ~/.local/bin/neon ~/.local/bin/cyber

echo ""
echo "✅ TEMA CYBERPUNK APLICADO COM SUCESSO!"
echo ""
echo "🌆 CYBERPUNK NEON HACKER THEME ATIVADO!"
echo ""
echo "📋 Para usar o Sway com tema Cyberpunk:"
echo "1. Faça logout da sessão atual"
echo "2. Na tela de login, escolha 'Sway' como sessão"
echo "3. Faça login normalmente"
echo ""
echo "🎯 Atalhos principais do Sway:"
echo "• Super + Enter: Terminal Kitty (transparente + neon)"
echo "• Super + D: Rofi launcher (cyberpunk)"
echo "• Super + Shift + Q: Fechar janela"
echo "• Super + 1-9: Trocar workspace"
echo ""
echo "🔥 Comandos cyberpunk disponíveis:"
echo "• matrix  - Efeito Matrix"
echo "• hack    - Simulação de hack"
echo "• neon    - Info do sistema neon"
echo "• cyber   - Menu de ferramentas"
echo "• btop    - Monitor do sistema"
echo ""
echo "🎨 Características do tema:"
echo "• Terminal transparente com cores neon"
echo "• Bordas com glow effect"
echo "• Cores: Rosa neon, Ciano, Verde Matrix"
echo "• Efeitos de transparência e blur"
echo "• ASCII art cyberpunk"
echo ""
echo "🚀 Reinicie o terminal para ver o banner cyberpunk!"
