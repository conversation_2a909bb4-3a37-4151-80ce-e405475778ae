#!/bin/bash

echo "🎨 Aplicando configurações do tema Tokyo Night..."

# Aplicar configurações GTK
echo "📱 Configurando GTK..."
gsettings set org.gnome.desktop.interface gtk-theme 'Breeze-Dark'
gsettings set org.gnome.desktop.interface icon-theme 'breeze-dark'
gsettings set org.gnome.desktop.interface cursor-theme 'Layan-cursors'
gsettings set org.gnome.desktop.interface font-name 'Fira Sans 11'
gsettings set org.gnome.desktop.interface monospace-font-name 'JetBrains Mono Nerd Font 12'
gsettings set org.gnome.desktop.interface color-scheme 'prefer-dark'

# Configurar cursor
echo "🖱️ Configurando cursor..."
gsettings set org.gnome.desktop.interface cursor-theme 'Layan-cursors'
gsettings set org.gnome.desktop.interface cursor-size 24

# Configurar wallpaper (cor sólida Tokyo Night)
echo "🖼️ Configurando wallpaper..."
gsettings set org.gnome.desktop.background picture-uri ''
gsettings set org.gnome.desktop.background picture-uri-dark ''
gsettings set org.gnome.desktop.background primary-color '#1a1b26'
gsettings set org.gnome.desktop.background color-shading-type 'solid'

# Configurar terminal padrão
echo "💻 Configurando terminal padrão..."
gsettings set org.gnome.desktop.default-applications.terminal exec 'kitty'

# Recarregar cache de fontes
echo "🔤 Recarregando cache de fontes..."
fc-cache -fv

# Configurar variáveis de ambiente para Wayland
echo "🌊 Configurando variáveis Wayland..."
echo 'export MOZ_ENABLE_WAYLAND=1' >> ~/.bashrc
echo 'export QT_QPA_PLATFORM=wayland' >> ~/.bashrc
echo 'export GDK_BACKEND=wayland' >> ~/.bashrc
echo 'export XDG_CURRENT_DESKTOP=sway' >> ~/.bashrc
echo 'export XDG_SESSION_DESKTOP=sway' >> ~/.bashrc

echo ""
echo "✅ Configurações aplicadas com sucesso!"
echo ""
echo "📋 Para usar o Sway:"
echo "1. Faça logout da sessão atual"
echo "2. Na tela de login, escolha 'Sway' como sessão"
echo "3. Faça login normalmente"
echo ""
echo "🎯 Atalhos principais do Sway:"
echo "• Super + Enter: Abrir terminal (Kitty)"
echo "• Super + D: Launcher de aplicações"
echo "• Super + Shift + Q: Fechar janela"
echo "• Super + 1-9: Trocar workspace"
echo "• Super + Shift + C: Recarregar configuração"
echo ""
echo "🚀 Aplicações instaladas:"
echo "• Terminal: Kitty (com tema Tokyo Night)"
echo "• Bar: Waybar (com tema Tokyo Night)"
echo "• Monitor: btop"
echo "• Fetch: fastfetch"
echo "• Browser: Firefox"
echo ""
echo "🎨 Tema aplicado: Tokyo Night"
echo "• GTK: Breeze-Dark"
echo "• Ícones: Breeze-Dark"
echo "• Cursor: Layan-cursors"
echo "• Fonte: JetBrains Mono Nerd Font + Fira Sans"
